package com.emathias.periodic.db.converters

import androidx.room.TypeConverter
import com.cronutils.model.Cron
import com.cronutils.model.CronType
import com.cronutils.model.definition.CronDefinitionBuilder
import com.cronutils.parser.CronParser

class CronConverter {
    companion object {
        private val cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.UNIX)
        private val parser = CronParser(cronDefinition)
    }

    @TypeConverter
    fun fromString(value: String?): Cron? {
        return value?.let {
            try {
                parser.parse(it)
            } catch (e: Exception) {
                null
            }
        }
    }

    @TypeConverter
    fun toString(cron: Cron?): String? {
        return cron?.asString()
    }
}