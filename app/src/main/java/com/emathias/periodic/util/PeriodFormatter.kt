package com.emathias.periodic.util

import net.time4j.PlainDate
import net.time4j.SystemClock
import net.time4j.Weekday
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.Period
import java.time.ZoneId
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for formatting time periods and dates using Time4A library.
 * Provides human-readable formatting for recurring periods like "every week", "every month",
 * and relative dates like "next Wednesday".
 */
@Singleton
class PeriodFormatter @Inject constructor() {

    /**
     * Format a Java Period into human-readable recurring text
     * Examples: "every week", "every month", "every 2 weeks"
     */
    fun formatRecurringPeriod(period: Period, locale: Locale = Locale.getDefault()): String {
        return when {
            // Daily periods
            period.days == 1 && period.months == 0 && period.years == 0 -> "every day"
            period.days > 1 && period.months == 0 && period.years == 0 -> "every ${period.days} days"

            // Weekly periods
            period.days == 7 && period.months == 0 && period.years == 0 -> "every week"
            period.days == 14 && period.months == 0 && period.years == 0 -> "every 2 weeks"
            period.days % 7 == 0 && period.days > 0 && period.months == 0 && period.years == 0 -> {
                val weeks = period.days / 7
                "every $weeks weeks"
            }

            // Monthly periods
            period.months == 1 && period.days == 0 && period.years == 0 -> "every month"
            period.months > 1 && period.days == 0 && period.years == 0 -> "every ${period.months} months"

            // Yearly periods
            period.years == 1 && period.months == 0 && period.days == 0 -> "every year"
            period.years > 1 && period.months == 0 && period.days == 0 -> "every ${period.years} years"

            // Complex periods - fallback to simple string representation
            else -> "every $period"
        }
    }

    /**
     * Format the next occurrence of a specific day of the week
     * Example: "next Wednesday", "this Friday"
     */
    fun formatNextOccurrence(dayOfWeek: DayOfWeek, locale: Locale = Locale.getDefault()): String {
        val today = SystemClock.inLocalView().today()
        val targetWeekday = convertToTime4jWeekday(dayOfWeek)

        val nextOccurrence = today.with(PlainDate.DAY_OF_WEEK.setToNext(targetWeekday))
        val daysBetween = today.until(nextOccurrence, net.time4j.CalendarUnit.DAYS).toInt()

        return when (daysBetween) {
            0 -> "today"
            1 -> "tomorrow"
            in 2..6 -> "this ${dayOfWeek.name.lowercase().replaceFirstChar { it.uppercase() }}"
            7 -> "next ${dayOfWeek.name.lowercase().replaceFirstChar { it.uppercase() }}"
            else -> "in $daysBetween days"
        }
    }

    /**
     * Format an Instant as a relative time
     * Examples: "in 2 hours", "3 days ago", "next week"
     */
    fun formatRelativeTime(instant: Instant, locale: Locale = Locale.getDefault()): String {
        val now = Instant.now()
        val duration = java.time.Duration.between(now, instant)

        return when {
            duration.isNegative -> {
                val absDuration = duration.abs()
                when {
                    absDuration.toDays() > 0 -> "${absDuration.toDays()} days ago"
                    absDuration.toHours() > 0 -> "${absDuration.toHours()} hours ago"
                    absDuration.toMinutes() > 0 -> "${absDuration.toMinutes()} minutes ago"
                    else -> "just now"
                }
            }

            else -> {
                when {
                    duration.toDays() > 0 -> "in ${duration.toDays()} days"
                    duration.toHours() > 0 -> "in ${duration.toHours()} hours"
                    duration.toMinutes() > 0 -> "in ${duration.toMinutes()} minutes"
                    else -> "now"
                }
            }
        }
    }

    /**
     * Format a duration in a human-readable way
     * Examples: "2 hours 30 minutes", "1 week 3 days"
     */
    fun formatDuration(
        duration: java.time.Duration,
        locale: Locale = Locale.getDefault(),
    ): String {
        val days = duration.toDays()
        val hours = duration.toHours() % 24
        val minutes = duration.toMinutes() % 60

        return when {
            days > 0 -> "$days days $hours hours"
            hours > 0 -> "$hours hours $minutes minutes"
            minutes > 0 -> "$minutes minutes"
            else -> "${duration.seconds} seconds"
        }
    }

    /**
     * Convert Java DayOfWeek to Time4J Weekday
     */
    private fun convertToTime4jWeekday(dayOfWeek: DayOfWeek): Weekday {
        return when (dayOfWeek) {
            DayOfWeek.MONDAY -> Weekday.MONDAY
            DayOfWeek.TUESDAY -> Weekday.TUESDAY
            DayOfWeek.WEDNESDAY -> Weekday.WEDNESDAY
            DayOfWeek.THURSDAY -> Weekday.THURSDAY
            DayOfWeek.FRIDAY -> Weekday.FRIDAY
            DayOfWeek.SATURDAY -> Weekday.SATURDAY
            DayOfWeek.SUNDAY -> Weekday.SUNDAY
        }
    }

    /**
     * Format the next occurrence based on an Instant and optional Period
     * This is useful for scheduled items to show when they will next occur
     */
    fun formatNextScheduledOccurrence(
        firstOccurrence: Instant,
        interval: Period?,
        locale: Locale = Locale.getDefault(),
    ): String {
        val now = Instant.now()

        if (interval == null) {
            // One-time occurrence
            return if (firstOccurrence.isAfter(now)) {
                formatRelativeTime(firstOccurrence, locale)
            } else {
                "completed"
            }
        }

        // Recurring occurrence - find next occurrence
        val zoneId = ZoneId.systemDefault()
        val firstLocalDate = LocalDate.ofInstant(firstOccurrence, zoneId)
        val nowLocalDate = LocalDate.ofInstant(now, zoneId)

        var nextOccurrence = firstLocalDate
        while (!nextOccurrence.isAfter(nowLocalDate)) {
            nextOccurrence = nextOccurrence.plus(interval)
        }

        val nextInstant = nextOccurrence.atStartOfDay(zoneId).toInstant()
        return formatRelativeTime(nextInstant, locale)
    }
}
