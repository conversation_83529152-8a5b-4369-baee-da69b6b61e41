package com.emathias.periodic.util

import java.time.DayOfWeek
import java.time.Instant
import java.time.Period
import java.time.temporal.ChronoUnit

/**
 * Example usage of PeriodFormatter for demonstration purposes
 */
class PeriodFormatterExample {
    
    private val formatter = PeriodFormatter()
    
    fun demonstrateFormatting() {
        // Example 1: Format recurring periods
        println("=== Recurring Period Examples ===")
        println(formatter.formatRecurringPeriod(Period.ofDays(1)))     // "every day"
        println(formatter.formatRecurringPeriod(Period.ofDays(7)))     // "every week"
        println(formatter.formatRecurringPeriod(Period.ofDays(14)))    // "every 2 weeks"
        println(formatter.formatRecurringPeriod(Period.ofMonths(1)))   // "every month"
        println(formatter.formatRecurringPeriod(Period.ofMonths(3)))   // "every 3 months"
        println(formatter.formatRecurringPeriod(Period.ofYears(1)))    // "every year"
        
        // Example 2: Format next occurrence of weekdays
        println("\n=== Next Occurrence Examples ===")
        println(formatter.formatNextOccurrence(DayOfWeek.WEDNESDAY))   // "next Wednesday" or "this Wednesday"
        println(formatter.formatNextOccurrence(DayOfWeek.FRIDAY))      // "next Friday" or "this Friday"
        println(formatter.formatNextOccurrence(DayOfWeek.MONDAY))      // "next Monday" or "this Monday"
        
        // Example 3: Format relative times
        println("\n=== Relative Time Examples ===")
        val now = Instant.now()
        val inTwoHours = now.plus(2, ChronoUnit.HOURS)
        val inThreeDays = now.plus(3, ChronoUnit.DAYS)
        val twoHoursAgo = now.minus(2, ChronoUnit.HOURS)
        
        println(formatter.formatRelativeTime(inTwoHours))    // "in 2 hours"
        println(formatter.formatRelativeTime(inThreeDays))   // "in 3 days"
        println(formatter.formatRelativeTime(twoHoursAgo))   // "2 hours ago"
        
        // Example 4: Format scheduled occurrences
        println("\n=== Scheduled Occurrence Examples ===")
        val firstOccurrence = now.plus(1, ChronoUnit.DAYS)
        val weeklyInterval = Period.ofDays(7)
        val monthlyInterval = Period.ofMonths(1)
        
        println(formatter.formatNextScheduledOccurrence(firstOccurrence, weeklyInterval))
        println(formatter.formatNextScheduledOccurrence(firstOccurrence, monthlyInterval))
        println(formatter.formatNextScheduledOccurrence(firstOccurrence, null)) // One-time event
        
        // Example 5: Format durations
        println("\n=== Duration Examples ===")
        val duration1 = java.time.Duration.ofHours(2).plusMinutes(30)
        val duration2 = java.time.Duration.ofDays(1).plusHours(3)
        val duration3 = java.time.Duration.ofMinutes(45)
        
        println(formatter.formatDuration(duration1))  // "2 hours 30 minutes"
        println(formatter.formatDuration(duration2))  // "1 days 3 hours"
        println(formatter.formatDuration(duration3))  // "45 minutes"
    }
}
