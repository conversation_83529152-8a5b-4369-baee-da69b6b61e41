package com.emathias.periodic.util

import com.cronutils.descriptor.CronDescriptor
import com.cronutils.model.Cron
import com.cronutils.model.time.ExecutionTime
import net.time4j.PlainDate
import net.time4j.SystemClock
import net.time4j.Weekday
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for formatting cron expressions and dates using Time4A library.
 * Provides human-readable formatting for recurring schedules and relative dates.
 */
@Singleton
class CronFormatter @Inject constructor() {

    /**
     * Format a Cron expression into human-readable recurring text
     * Examples: "every week", "every month", "every day at 9:00 AM"
     */
    fun formatRecurringCron(cron: Cron, locale: Locale = Locale.getDefault()): String {
        return try {
            val descriptor = CronDescriptor.instance(locale)
            descriptor.describe(cron)
        } catch (e: Exception) {
            // Fallback to cron string if description fails
            cron.asString()
        }
    }

    /**
     * Format the next occurrence of a specific day of the week
     * Example: "next Wednesday", "this Friday"
     */
    fun formatNextOccurrence(dayOfWeek: DayOfWeek, locale: Locale = Locale.getDefault()): String {
        val today = SystemClock.inLocalView().today()
        val targetWeekday = convertToTime4jWeekday(dayOfWeek)

        val nextOccurrence = today.with(PlainDate.DAY_OF_WEEK.setToNext(targetWeekday))
        val daysBetween = today.until(nextOccurrence, net.time4j.CalendarUnit.DAYS).toInt()

        return when (daysBetween) {
            0 -> "today"
            1 -> "tomorrow"
            in 2..6 -> "this ${dayOfWeek.name.lowercase().replaceFirstChar { it.uppercase() }}"
            7 -> "next ${dayOfWeek.name.lowercase().replaceFirstChar { it.uppercase() }}"
            else -> "in $daysBetween days"
        }
    }

    /**
     * Format an Instant as a relative time
     * Examples: "in 2 hours", "3 days ago", "next week"
     */
    fun formatRelativeTime(instant: Instant, locale: Locale = Locale.getDefault()): String {
        val now = Instant.now()
        val duration = java.time.Duration.between(now, instant)

        return when {
            duration.isNegative -> {
                val absDuration = duration.abs()
                when {
                    absDuration.toDays() > 0 -> "${absDuration.toDays()} days ago"
                    absDuration.toHours() > 0 -> "${absDuration.toHours()} hours ago"
                    absDuration.toMinutes() > 0 -> "${absDuration.toMinutes()} minutes ago"
                    else -> "just now"
                }
            }

            else -> {
                when {
                    duration.toDays() > 0 -> "in ${duration.toDays()} days"
                    duration.toHours() > 0 -> "in ${duration.toHours()} hours"
                    duration.toMinutes() > 0 -> "in ${duration.toMinutes()} minutes"
                    else -> "now"
                }
            }
        }
    }

    /**
     * Format a duration in a human-readable way
     * Examples: "2 hours 30 minutes", "1 week 3 days"
     */
    fun formatDuration(
        duration: java.time.Duration,
        locale: Locale = Locale.getDefault(),
    ): String {
        val days = duration.toDays()
        val hours = duration.toHours() % 24
        val minutes = duration.toMinutes() % 60

        return when {
            days > 0 -> "$days days $hours hours"
            hours > 0 -> "$hours hours $minutes minutes"
            minutes > 0 -> "$minutes minutes"
            else -> "${duration.seconds} seconds"
        }
    }

    /**
     * Convert Java DayOfWeek to Time4J Weekday
     */
    private fun convertToTime4jWeekday(dayOfWeek: DayOfWeek): Weekday {
        return when (dayOfWeek) {
            DayOfWeek.MONDAY -> Weekday.MONDAY
            DayOfWeek.TUESDAY -> Weekday.TUESDAY
            DayOfWeek.WEDNESDAY -> Weekday.WEDNESDAY
            DayOfWeek.THURSDAY -> Weekday.THURSDAY
            DayOfWeek.FRIDAY -> Weekday.FRIDAY
            DayOfWeek.SATURDAY -> Weekday.SATURDAY
            DayOfWeek.SUNDAY -> Weekday.SUNDAY
        }
    }

    /**
     * Format the next occurrence based on an Instant and optional Cron expression
     * This is useful for scheduled items to show when they will next occur
     */
    fun formatNextScheduledOccurrence(
        firstOccurrence: Instant,
        cronExpression: Cron?,
        locale: Locale = Locale.getDefault(),
    ): String {
        val now = Instant.now()

        if (cronExpression == null) {
            // One-time occurrence
            return if (firstOccurrence.isAfter(now)) {
                formatRelativeTime(firstOccurrence, locale)
            } else {
                "completed"
            }
        }

        // Recurring occurrence - find next occurrence using cron
        val executionTime = ExecutionTime.forCron(cronExpression)
        val nowZoned = now.atZone(ZoneId.systemDefault())
        val nextExecution = executionTime.nextExecution(nowZoned)

        return if (nextExecution.isPresent) {
            formatRelativeTime(nextExecution.get().toInstant(), locale)
        } else {
            "no future occurrences"
        }
    }
}
