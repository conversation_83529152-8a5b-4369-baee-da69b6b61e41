package com.emathias.periodic.util

import org.junit.Test
import org.junit.Assert.*
import java.time.DayOfWeek
import java.time.Period

/**
 * Test class for PeriodFormatter to verify Time4A integration
 */
class PeriodFormatterTest {

    private val formatter = PeriodFormatter()

    @Test
    fun testFormatRecurringPeriod_Daily() {
        val period = Period.ofDays(1)
        val result = formatter.formatRecurringPeriod(period)
        assertEquals("every day", result)
    }

    @Test
    fun testFormatRecurringPeriod_Weekly() {
        val period = Period.ofDays(7)
        val result = formatter.formatRecurringPeriod(period)
        assertEquals("every week", result)
    }

    @Test
    fun testFormatRecurringPeriod_BiWeekly() {
        val period = Period.ofDays(14)
        val result = formatter.formatRecurringPeriod(period)
        assertEquals("every 2 weeks", result)
    }

    @Test
    fun testFormatRecurringPeriod_Monthly() {
        val period = Period.ofMonths(1)
        val result = formatter.formatRecurringPeriod(period)
        assertEquals("every month", result)
    }

    @Test
    fun testFormatRecurringPeriod_Quarterly() {
        val period = Period.ofMonths(3)
        val result = formatter.formatRecurringPeriod(period)
        assertEquals("every 3 months", result)
    }

    @Test
    fun testFormatRecurringPeriod_Yearly() {
        val period = Period.ofYears(1)
        val result = formatter.formatRecurringPeriod(period)
        assertEquals("every year", result)
    }

    @Test
    fun testFormatRecurringPeriod_MultipleDays() {
        val period = Period.ofDays(3)
        val result = formatter.formatRecurringPeriod(period)
        assertEquals("every 3 days", result)
    }

    @Test
    fun testFormatNextOccurrence_Wednesday() {
        // Note: This test will depend on the current day, so we're just checking it doesn't crash
        val result = formatter.formatNextOccurrence(DayOfWeek.WEDNESDAY)
        assertNotNull(result)
        assertTrue(result.isNotEmpty())
    }

    @Test
    fun testFormatNextOccurrence_Friday() {
        // Note: This test will depend on the current day, so we're just checking it doesn't crash
        val result = formatter.formatNextOccurrence(DayOfWeek.FRIDAY)
        assertNotNull(result)
        assertTrue(result.isNotEmpty())
    }

    @Test
    fun testFormatDuration_HoursAndMinutes() {
        val duration = java.time.Duration.ofHours(2).plusMinutes(30)
        val result = formatter.formatDuration(duration)
        assertEquals("2 hours 30 minutes", result)
    }

    @Test
    fun testFormatDuration_DaysAndHours() {
        val duration = java.time.Duration.ofDays(1).plusHours(3)
        val result = formatter.formatDuration(duration)
        assertEquals("1 days 3 hours", result)
    }

    @Test
    fun testFormatDuration_MinutesOnly() {
        val duration = java.time.Duration.ofMinutes(45)
        val result = formatter.formatDuration(duration)
        assertEquals("45 minutes", result)
    }
}
